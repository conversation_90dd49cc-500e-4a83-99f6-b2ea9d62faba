"""
DATA LOADING AND INITIAL EXPLORATION
====================================

This section loads the crime dataset and performs initial data exploration.
We import essential libraries for data manipulation, analysis, and visualization.

Key Libraries:
- pandas: Data manipulation and analysis
- numpy: Numerical computing
- matplotlib: Static plotting
- seaborn: Statistical data visualization

Data Loading Notes:
- TIME OCC is loaded as string to preserve leading zeros (e.g., '0100' for 1:00 AM)
- This prevents automatic conversion to integer which would lose time formatting
"""

# Import required libraries for data analysis and visualization
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Configure visualization settings for better presentation
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 11

# Load the crime dataset
# Note: TIME OCC is loaded as string to preserve time formatting (e.g., '0100' for 1:00 AM)
crimes = pd.read_csv("crimes.csv", dtype={"TIME OCC": str})

# Display basic information about the dataset
print(f"Dataset Shape: {crimes.shape[0]:,} rows × {crimes.shape[1]} columns")
print(f"Memory Usage: {crimes.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
print("\n📊 First 5 rows of the dataset:")
crimes.head()

"""
PEAK CRIME HOUR ANALYSIS
========================

This analysis extracts the hour from the TIME OCC column and identifies
the hour with the highest frequency of crimes.

Steps:
1. Parse TIME OCC string to extract hour (first 2 characters)
2. Convert to integer for numerical analysis
3. Find the mode (most frequent value) to identify peak hour
4. Store result for business reporting
"""

# Extract hour from TIME OCC column (24-hour format)
# TIME OCC format: 'HHMM' (e.g., '1200' = 12:00 PM, '0100' = 1:00 AM)
crimes["HOUR_OCC"] = crimes["TIME OCC"].str[:2].astype(int)

# Calculate hourly crime frequency for analysis
hourly_counts = crimes["HOUR_OCC"].value_counts().sort_index()

# Identify peak crime hour using statistical mode
peak_crime_hour = crimes["HOUR_OCC"].mode()[0]

# Display results
print(f"🚨 PEAK CRIME HOUR ANALYSIS RESULTS")
print(f"{'='*40}")
print(f"Peak Crime Hour: {peak_crime_hour}:00 (24-hour format)")
print(f"Total Crimes at Peak Hour: {hourly_counts[peak_crime_hour]:,}")
print(f"Percentage of Total Crimes: {(hourly_counts[peak_crime_hour]/len(crimes)*100):.1f}%")
print(f"\n📊 This represents {hourly_counts[peak_crime_hour]:,} out of {len(crimes):,} total crimes")

"""
TEMPORAL CRIME PATTERN VISUALIZATION
====================================

This section creates a comprehensive visualization showing crime frequency
distribution across all 24 hours of the day, highlighting the peak crime hour.

Visualization Features:
- Histogram showing crime distribution by hour
- Highlighted peak crime hour with annotation
- Professional styling for portfolio presentation
- Statistical summary of top crime hours
"""

# Create comprehensive visualization of crime frequency by hour
plt.figure(figsize=(14, 8))

# Create histogram showing crime distribution across 24 hours
bars = plt.bar(hourly_counts.index, hourly_counts.values, 
               alpha=0.7, color='steelblue', edgecolor='navy', linewidth=0.5)

# Highlight the peak crime hour with different color
bars[peak_crime_hour].set_color('crimson')
bars[peak_crime_hour].set_alpha(0.9)

# Add vertical line at peak hour
plt.axvline(x=peak_crime_hour, color='red', linestyle='--', linewidth=2, alpha=0.8)

# Customize the plot with professional styling
plt.title("📊 Crime Frequency Distribution by Hour of Day\nLos Angeles Crime Data Analysis", 
          fontsize=16, fontweight='bold', pad=20)
plt.xlabel("Hour of Day (24-hour format)", fontsize=12, fontweight='bold')
plt.ylabel("Number of Crimes", fontsize=12, fontweight='bold')

# Set x-axis ticks for all 24 hours
plt.xticks(range(0, 24), [f'{h:02d}:00' for h in range(24)], rotation=45)
plt.grid(True, alpha=0.3, axis='y')

# Add annotation for peak hour with crime count
peak_count = hourly_counts[peak_crime_hour]
plt.annotate(f'Peak Hour\n{peak_count:,} crimes\n({peak_count/len(crimes)*100:.1f}% of total)', 
            xy=(peak_crime_hour, peak_count), 
            xytext=(peak_crime_hour + 3, peak_count + 500),
            arrowprops=dict(arrowstyle='->', color='red', lw=2),
            fontsize=11, fontweight='bold',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.7))

plt.tight_layout()
plt.show()

# Display comprehensive statistical summary
print("\n📈 HOURLY CRIME STATISTICS SUMMARY")
print("="*50)
print(f"Total Crimes Analyzed: {len(crimes):,}")
print(f"Peak Crime Hour: {peak_crime_hour}:00 ({peak_count:,} crimes)")
print(f"Lowest Crime Hour: {hourly_counts.idxmin()}:00 ({hourly_counts.min():,} crimes)")
print(f"Average Crimes per Hour: {hourly_counts.mean():.0f}")
print(f"Standard Deviation: {hourly_counts.std():.0f}")

print("\n🏆 TOP 5 CRIME HOURS:")
print("-" * 30)
top_hours = hourly_counts.sort_values(ascending=False).head(5)
for i, (hour, count) in enumerate(top_hours.items(), 1):
    print(f"{i}. Hour {hour:2d}:00 - {count:,} crimes ({count/len(crimes)*100:.1f}%)")

print("\n💡 BUSINESS INSIGHTS:")
print("-" * 20)
if peak_crime_hour >= 12 and peak_crime_hour <= 18:
    print("• Peak crime occurs during afternoon/evening hours")
    print("• Recommend increased patrol presence during business hours")
elif peak_crime_hour >= 19 or peak_crime_hour <= 5:
    print("• Peak crime occurs during night/early morning hours")
    print("• Recommend enhanced night patrol and lighting in high-crime areas")
else:
    print("• Peak crime occurs during morning hours")
    print("• Consider morning patrol adjustments and community awareness programs")

"""
NIGHT CRIME HOTSPOT ANALYSIS
============================

This analysis identifies areas with the highest frequency of night crimes,
defined as crimes occurring between 10 PM (22:00) and 3:59 AM.

Night Crime Definition:
- Time window: 22:00 - 03:59 (10 PM - 3:59 AM)
- Includes late evening and early morning hours
- Critical for night patrol resource allocation

Analysis Steps:
1. Create boolean flag for night crimes
2. Filter dataset for night crimes only
3. Group by area and count occurrences
4. Identify peak night crime location
"""

# Define night crime time window (22:00-03:59)
# Using logical OR to capture both late night (22-23) and early morning (0-3) hours
crimes["NIGHT_CRIME"] = (crimes["HOUR_OCC"].between(22, 23)) | (crimes["HOUR_OCC"].between(0, 3))

# Filter for night crimes only
night_crimes = crimes[crimes["NIGHT_CRIME"] == True]

# Calculate night crime frequency by area
night_crime_by_area = night_crimes["AREA NAME"].value_counts()

# Identify peak night crime location
peak_night_crime_location = night_crimes["AREA NAME"].mode()[0]

# Display comprehensive results
print(f"🌙 NIGHT CRIME HOTSPOT ANALYSIS RESULTS")
print(f"{'='*45}")
print(f"Night Crime Definition: 22:00-03:59 (10 PM - 3:59 AM)")
print(f"Total Night Crimes: {len(night_crimes):,} ({len(night_crimes)/len(crimes)*100:.1f}% of all crimes)")
print(f"Peak Night Crime Location: {peak_night_crime_location}")
print(f"Night Crimes in Peak Area: {night_crime_by_area[peak_night_crime_location]:,}")
print(f"Percentage of Total Night Crimes: {(night_crime_by_area[peak_night_crime_location]/len(night_crimes)*100):.1f}%")

"""
NIGHT CRIME GEOGRAPHICAL VISUALIZATION
======================================

This section creates visualizations to show the geographical distribution
of night crimes across different areas in Los Angeles.

Visualization Features:
- Horizontal bar chart showing night crime frequency by area
- Highlighted peak night crime location
- Statistical summary and business insights
"""

# Create comprehensive visualization of night crimes by area
plt.figure(figsize=(14, 10))

# Get top 15 areas for better visualization
top_night_areas = night_crime_by_area.head(15)

# Create horizontal bar chart
bars = plt.barh(range(len(top_night_areas)), top_night_areas.values, 
                color='steelblue', alpha=0.7, edgecolor='navy')

# Highlight the peak night crime area
peak_index = top_night_areas.index.get_loc(peak_night_crime_location)
bars[peak_index].set_color('crimson')
bars[peak_index].set_alpha(0.9)

# Customize the plot
plt.title('🌙 Night Crime Distribution by Area (Top 15)\nLos Angeles Crime Data Analysis', 
          fontsize=16, fontweight='bold', pad=20)
plt.xlabel('Number of Night Crimes (22:00 - 03:59)', fontsize=12, fontweight='bold')
plt.ylabel('Police Area', fontsize=12, fontweight='bold')

# Set y-axis labels
plt.yticks(range(len(top_night_areas)), top_night_areas.index)
plt.gca().invert_yaxis()  # Highest values at top

# Add value labels on bars
for i, (area, count) in enumerate(top_night_areas.items()):
    plt.text(count + 10, i, f'{count:,}', 
             va='center', fontweight='bold' if area == peak_night_crime_location else 'normal')

# Add annotation for peak area
peak_count = night_crime_by_area[peak_night_crime_location]
plt.annotate(f'Peak Night Crime Area\n{peak_count:,} crimes', 
            xy=(peak_count, peak_index), 
            xytext=(peak_count + 200, peak_index + 2),
            arrowprops=dict(arrowstyle='->', color='red', lw=2),
            fontsize=11, fontweight='bold',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.7))

plt.grid(True, alpha=0.3, axis='x')
plt.tight_layout()
plt.show()

# Display comprehensive statistical summary
print("\n🌙 NIGHT CRIME ANALYSIS SUMMARY")
print("="*40)
print(f"Night Crime Time Window: 22:00-03:59 (10 PM - 3:59 AM)")
print(f"Total Night Crimes: {len(night_crimes):,} ({len(night_crimes)/len(crimes)*100:.1f}% of all crimes)")
print(f"Peak Night Crime Area: {peak_night_crime_location}")
print(f"Night Crimes in Peak Area: {night_crime_by_area[peak_night_crime_location]:,}")
print(f"Peak Area Share: {(night_crime_by_area[peak_night_crime_location]/len(night_crimes)*100):.1f}% of night crimes")

print("\n🏆 TOP 5 NIGHT CRIME AREAS:")
print("-" * 35)
for i, (area, count) in enumerate(night_crime_by_area.head(5).items(), 1):
    percentage = (count/len(night_crimes)*100)
    print(f"{i}. {area:<15} - {count:,} crimes ({percentage:.1f}%)")

print("\n💡 BUSINESS INSIGHTS:")
print("-" * 20)
print(f"• {peak_night_crime_location} requires enhanced night patrol presence")
print(f"• Night crimes represent {len(night_crimes)/len(crimes)*100:.1f}% of total crime volume")
print(f"• Top 5 areas account for {night_crime_by_area.head(5).sum()/len(night_crimes)*100:.1f}% of night crimes")
print("• Consider implementing targeted night safety programs in high-crime areas")
print("• Coordinate with local businesses for improved lighting and security measures")

sns.set_style("whitegrid")
sns.set_palette("pastel")
sns.countplot(x="AREA NAME", data=crimes[crimes["NIGHT_CRIME"] == True])
plt.title("Frequency of Night Crimes by Location")
plt.xlabel("Location")
plt.ylabel("Frequency")
plt.xticks(rotation=90)
plt.show()

# Create age bins and labels
age_bins = [0, 17, 25, 34, 44, 54, 64, float('inf')]
age_labels = ["0-17", "18-25", "26-34", "35-44", "45-54", "55-64", "65+"]

# Categorize victim ages into bins
crimes["AGE_GROUP"] = pd.cut(crimes["Vict Age"], bins=age_bins, labels=age_labels)

# Count crimes by age group and convert to Series
victim_ages = crimes["AGE_GROUP"].value_counts().sort_index()
print(victim_ages)

sns.barplot(x=victim_ages.index, y=victim_ages.values)
plt.xlabel("Victim Age Group")
plt.ylabel("Number of Crimes")
plt.title("Crimes by Victim Age Group")
plt.show()