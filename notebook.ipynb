# Re-run this cell
# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
crimes = pd.read_csv("crimes.csv", dtype={"TIME OCC": str})
crimes.head()

1. Which hour has the highest frequency of crimes? Store as an integer variable called peak_crime_hour.

crimes["HOUR_OCC"] = crimes["TIME OCC"].str[:2].astype(int)
peak_crime_hour = crimes["HOUR_OCC"].mode()[0]
print(peak_crime_hour)

sns.histplot(crimes["HOUR_OCC"], bins=24, kde=False)
plt.title("Frequency of Crimes by Hour")
plt.xlabel("Hour")
plt.ylabel("Frequency")
plt.show()

crimes["NIGHT_CRIME"] = crimes["HOUR_OCC"].between(22, 23) | crimes["HOUR_OCC"].between(0, 5)
peak_night_crime_location = crimes[crimes["NIGHT_CRIME"] == True]["AREA NAME"].mode()[0]
print(peak_night_crime_location)

sns.set_style("whitegrid")
sns.set_palette("pastel")
sns.countplot(x="AREA NAME", data=crimes[crimes["NIGHT_CRIME"] == True])
plt.title("Frequency of Night Crimes by Location")
plt.xlabel("Location")
plt.ylabel("Frequency")
plt.xticks(rotation=90)
plt.show()

# Create age bins and labels
age_bins = [0, 17, 25, 34, 44, 54, 64, float('inf')]
age_labels = ["0-17", "18-25", "26-34", "35-44", "45-54", "55-64", "65+"]

# Categorize victim ages into bins
crimes["AGE_GROUP"] = pd.cut(crimes["Vict Age"], bins=age_bins, labels=age_labels)

# Count crimes by age group and convert to Series
victim_ages = crimes["AGE_GROUP"].value_counts().sort_index()
print(victim_ages)

sns.barplot(x=victim_ages.index, y=victim_ages.values)
plt.xlabel("Victim Age Group")
plt.ylabel("Number of Crimes")
plt.title("Crimes by Victim Age Group")
plt.show()